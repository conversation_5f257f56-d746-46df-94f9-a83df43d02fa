<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"
        integrity="sha512-Fo3rlrZj/k7ujTnHg4CGR2D7kSs0v4LLanw2qksYuRlEzO+tcaEPQogQ0KaoGN26/zrn20ImR1DfuLWnOo7aBA=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"
        integrity="sha512-894YE6QWD5I59HgZOGReFYm4dnWc1Qt5NtvYSaNcOP+u1T9qYdvdihz0PPSiiqn/+/3e7Jo4EaG7TubfWGUrMQ=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>
</head>

<body>
    <style>
        body {
            background: transparent;
            direction: rtl;
            text-align: right;
            padding: 25px 20px;
            overflow-y: hidden;
            overflow-x: hidden;
        }

        h2 {
            margin: 0;
            padding: 0;
            border: none;
        }

        #timer {
            font-size: 20px;
            font-family: sans-serif;
            text-shadow: 0px 0 5px #25f300;
            color: #00ff80;
            <!-- font-style: italic; -->
            <!-- border: 1px solid #fff;  -->
        }

        .carhud {
            top: 85%;
            position: absolute;
            display: none;
        }

        .playerinfo {
            display: block;
            top: 7%;
            border-radius: 10px;
            position: absolute;
            font-style: normal;
            text-shadow: 10px 5 10px #000000;
            right: 10px;
            /* background-color: rgba(0, 0, 0, 0.623); */
            padding: 10px;
        }

        .speed {
            font-size: 14px;
        }

        .fa-tachometer-alt {
            color: #0061ff;
        }

        .fa-cogs {
            color: #0061ff;
        }

        .fa-gas-pump {
            color: #0061ff;
        }

        .show {
            display: block;
        }

        .hide {
            display: none;
        }

        .lowfuel {
            animation-name: lowfuel;
            animation-duration: 2s;
            animation-fill-mode: inherit;
            animation-iteration-count: infinite;
        }

        @keyframes lowfuel {
            0% {
                text-shadow: 0 0 0px #0061ff;
                color: #0061ff;
            }

            50% {
                text-shadow: 0 0 5px #ff002b;
                color: #ff002b;
            }

            100% {
                text-shadow: 0 0 0px #0061ff;
                color: #0061ff;
            }
        }

        span {
            text-shadow: 0 0 5px #000;
        }

        h2 {
            margin-top: 8px;
            font-size: 11px;
            font-family: sans-serif;

            color: #ffffff;
            text-shadow: 0 0 5px #000;
            <!-- font-style: italic; -->
        }

        @font-face {
            font-family: samsung;
            src: url('./fonts/SamsungSans-Regular.woff');
        }
    </style>
    <!-- <h2 id="timer">Modern Land RP</h2> -->
    <!-- <h2 id="day">Loading</h2> -->
    <!-- <h2 id="clock">Loading</h2> -->
    <div class="playerinfo">
        <h2 class="speed" id="playername">Loading</h2>
        <h2 class="speed" id="playerjob">Loading</h2>
        <h2 class="speed" id="playergang">Loading</h2>
        <h2 class="speed" id="playermoney">Loading</h2>
        <h2 class="speed" id="playerid">Loading</h2>
    </div>
    <script>

        $("#day").fadeOut();
        $("#clock").fadeOut();
        
        $("#timer").html("");        
        $("#timer").css("font-size","25px")
        $("#timer").fadeIn(1);
        <!-- $("#timer").html("Modern Land RP");   -->
        $(".playerinfo").fadeIn();

        let visable = false
        var days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
        var months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        var d = new Date();
        var tgggg = false
        setInterval(() => {
            $("#day").html(`${days[d.getDay()]} ${d.getDate()} ${months[d.getMonth()]} ${d.getFullYear()}`);
        }, 240000);
        $("#day").html(`${days[d.getDay()]} ${d.getDate()} ${months[d.getMonth()]} ${d.getFullYear()}`);
        window.addEventListener("message", (event) => {
            if (event.data.show == true) {
                $('.carhud').fadeIn();
            }
            if (event.data.show == false) {
                $('.carhud').fadeOut(100);
            }
            if (event.data.action === "carhud") {
                if (Math.round(event.data.fuel) <= 30) {
                    $("#speedo").html(`${Math.round(event.data.speed)}KM`);
                    $("#gear").html(event.data.gear);
                    $(".fa-gas-pump").addClass("lowfuel");
                    $("#fuel").addClass("lowfuel");
                    // $(".fa-gas-pump").css("color", "#ff002b");
                    $("#fuel").html(`${Math.round(event.data.fuel)}%`);
                } else {
                    $("#speedo").html(`${Math.round(event.data.speed)}KM`);
                    $("#gear").html(event.data.gear);
                    $(".fa-gas-pump").removeClass("lowfuel");
                    $("#fuel").removeClass("lowfuel");

                    $("#fuel").html(`${Math.round(event.data.fuel)}%`);
                }
            }
            if (event.data.action === "time") {
                $("#clock").html(event.data.clock);
            }


            
            
            if (event.data.action == "playerinfohide") {
                if (tgggg) {
                    $('.playerinfo').fadeOut();
                    $('#clock').fadeOut();
                    $('#day').fadeOut();
                    $('#timer').fadeOut();
                    // visable = !visable;
                    tgggg = !tgggg;
                } else {
                    $('.playerinfo').fadeIn();
                    $('#clock').fadeIn();
                    $('#day').fadeIn();
                    $('#timer').fadeIn();
                    // visable = !visable;
                    tgggg = !tgggg;
                }
            }

            if (event.data.action === "playerinfo") {
                if (visable) {
                    $('.playerinfo').fadeOut();
            
                    $("#day").fadeIn();
                    $("#clock").fadeIn();
                    $("#timer").css("font-size","14px")
                } else {
                    $("#day").fadeOut();
                    $("#clock").fadeOut();
                    
                    $("#timer").html("");
                    
                    
                    $("#timer").css("font-size","25px")
                    $("#timer").fadeIn(1);
                    <!-- setTimeout(() => { -->
                        <!-- $("#timer").html("M");  -->
                    <!-- }, 100); -->
                    <!-- setTimeout(() => { -->
                        <!-- $("#timer").html("Mo");   -->
                    <!-- }, 300); -->
                    <!-- setTimeout(() => { -->
                        <!-- $("#timer").html("Mod");   -->
                    <!-- }, 500); -->
                    <!-- setTimeout(() => { -->
                        <!-- $("#timer").html("Mode");   -->
                    <!-- }, 700); -->
                    <!-- setTimeout(() => { -->
                        <!-- $("#timer").html("Moder");   -->
                    <!-- }, 900);  -->
                    <!-- setTimeout(() => { -->
                        <!-- $("#timer").html("Modern");   -->
                    <!-- }, 1200); -->
                    <!-- setTimeout(() => { -->
                        <!-- $("#timer").html("Modern L");   -->
                    <!-- }, 1500); -->
                    <!-- setTimeout(() => { -->
                        <!-- $("#timer").html("Modern La");   -->
                    <!-- }, 1800); -->
					<!-- setTimeout(() => { -->
                        <!-- $("#timer").html("Modern Lan");   -->
                    <!-- }, 2100); -->
					<!-- setTimeout(() => { -->
                        <!-- $("#timer").html("Modern Land");   -->
                    <!-- }, 2400); -->
					<!-- setTimeout(() => { -->
                        <!-- $("#timer").html("Modern Land R");   -->
                    <!-- }, 2700); -->
					<!-- setTimeout(() => { -->
                        <!-- $("#timer").html("Modern Land RP");   -->
                    <!-- }, 3000); -->
                    $(".playerinfo").fadeIn();
                   
                }
                visable = !visable;
            }
            if (event.data.action === "playerdata") {
                $("#playername").html(`<i class="fa-solid fa fa-id-card" style="color:#eb00b8;text-shadow:none;"></i> ${event.data.playername}`);
                $("#playerjob").html(`<i class="fa-solid fa fa-briefcase" style="color:#eb00b8;text-shadow:none;"></i> ${event.data.job}`);
                $("#playergang").html(`<i class="fa-solid fas fa-skull-crossbones" style="color:#eb00b8;text-shadow:none;"></i> ${event.data.gang}`);
                $("#playermoney").html(`<i class="fa-solid fas fa-wallet" style="color:#eb00b8;text-shadow:none;"></i> ${event.data.money}`);
                $("#playerid").html(`<i class="fa-solid fas fa-user-alt" style="color:#eb00b8;text-shadow:none;"></i> ${event.data.playerid}`);
            }
            
        })
    </script>
    
</body>

</html>