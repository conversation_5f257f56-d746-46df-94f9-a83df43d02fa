-- Developer Dr.<PERSON> = nil
Citizen.CreateThread(function()
    while ESX == nil do
        TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)
        Citizen.Wait(0)
	end
end)

RegisterNetEvent("esx:playerLoaded",function(xPlayer)
	ESX.TriggerServerCallback('GetPlayerInfo', function(Info)
		SendNUIMessage({
			action = "playerdata",
			playername = string.gsub(Info.name, "_", " "),
			money = MakeDigit(Info.money),
			playerid = GetPlayerServerId(PlayerId()),
			job = Info.job.label .. " | " .. Info.job.grade_label,
			gang = Info.gang.name .. " | " .. Info.gang.grade_label
		})         
	end)
end)

RegisterNetEvent("esx:setJob",function(job)
	ESX.TriggerServerCallback('GetPlayerInfo', function(Info)
		SendNUIMessage({
			action = "playerdata",
			playername = string.gsub(Info.name, "_", " "),
			money = MakeDigit(Info.money),
			playerid = GetPlayerServerId(PlayerId()),
			job = job.label .. " | " .. job.grade_label,
			gang = Info.gang.name .. " | " .. Info.gang.grade_label
		})        
	end)
end)

RegisterNetEvent("esx:setGang",function(gang)
	ESX.TriggerServerCallback('GetPlayerInfo', function(Info)
		SendNUIMessage({
			action = "playerdata",
			playername = string.gsub(Info.name, "_", " "),
			money = MakeDigit(Info.money),
			playerid = GetPlayerServerId(PlayerId()),
			job = Info.job.label .. " | " .. Info.job.grade_label,
			gang = gang.name .. " | " .. gang.grade_label
		})         
	end)
end)

RegisterNetEvent("moneyUpdate",function(cash)
	ESX.TriggerServerCallback('GetPlayerInfo', function(Info)
		SendNUIMessage({
			action = "playerdata",
			playername = string.gsub(Info.name, "_", " "),
			money = MakeDigit(cash),
			playerid = GetPlayerServerId(PlayerId()),
			job = Info.job.label .. " | " .. Info.job.grade_label,
			gang = Info.gang.name .. " | " .. Info.gang.grade_label
		})         
	end)
end)

RegisterNetEvent("nameUpdate",function(name)
	ESX.TriggerServerCallback('GetPlayerInfo', function(Info)
		SendNUIMessage({
			action = "playerdata",
			playername = string.gsub(name, "_", " "),
			money = MakeDigit(Info.money),
			playerid = GetPlayerServerId(PlayerId()),
			job = Info.job.label .. " | " .. Info.job.grade_label,
			gang = Info.gang.name .. " | " .. Info.gang.grade_label
		})         
	end)
end)

RegisterNetEvent("onKeyUP",function(key)
    if key == "g" then
		TriggerEvent("ReloadHuD")
        -- SendNUIMessage({action = "playerinfo"})
    end
end)
 
RegisterNetEvent("ReloadHuD", function()
	ESX.TriggerServerCallback('GetPlayerInfo', function(Info)
		SendNUIMessage({
			action = "playerdata",
			playername = string.gsub(Info.name, "_", " "),
			money = MakeDigit(Info.money),
			playerid = GetPlayerServerId(PlayerId()),
			job = Info.job.label .. " | " .. Info.job.grade_label,
			gang = Info.gang.name .. " | " .. Info.gang.grade_label
		})         
	end)
end)

-- Citizen.CreateThread(function()
    -- while true do
        -- ESX.TriggerServerCallback('GetPlayerInfo', function(Info)
            -- SendNUIMessage({
                -- action = "playerdata",
                -- playername = string.gsub(Info.name, "_", " "),
                -- money = MakeDigit(Info.money),
                -- playerid = GetPlayerServerId(PlayerId()),
                -- job = Info.job.label .. " | " .. Info.job.grade_label,
                -- gang = Info.gang.name .. " | " .. Info.gang.grade_label
            -- })         
        -- end)
        -- Citizen.Wait(8000)
    -- end
-- end)

function time()
    hour = GetClockHours()
    minute = GetClockMinutes()
    if hour <= 9 then
        hour = "0" .. hour
    end
    if minute <= 9 then
        minute = "0" .. minute
    end
    return hour .. ":" .. minute
end
function MakeDigit(j)
    local k, l, m = string.match(j, "^([^%d]*%d)(%d*)(.-)$")
    return "$" .. k .. l:reverse():gsub("(%d%d%d)", "%1" .. ","):reverse() .. m
end

CreateThread(function()
    while true do
        Citizen.Wait(10000)
        SendNUIMessage({action = "time", clock = time(), name = GetPlayerName(source)})
    end
end)

local isPaused, ToggleHUD = true, true

CreateThread(function()
	local sleep = 300
	while true do
		Wait(sleep)
		if IsPauseMenuActive() and not isPaused and ToggleHUD then
			isPaused = true
            -- SendNUIMessage({action = "playerinfo"})
            SendNUIMessage({action = "playerinfohide"})
		elseif not IsPauseMenuActive() and isPaused and ToggleHUD then
			isPaused = false 
            SendNUIMessage({action = "playerinfohide"})
            -- SendNUIMessage({action = "playerinfo"})
		end
	end
end)
-- Developer Dr.Mohammad 
